{"timestamp": "2025-07-24T07:21:36.810494", "summary": {"total_tests": 18, "passed": 16, "failed": 2, "errors": 0, "skipped": 0, "pass_rate": 88.89}, "categories": {"System": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.0217397212982178, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:21:35.753423"}], "A. Conversational AI": [{"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.002920866012573242, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.756699"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.003603696823120117, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.760413"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.0017499923706054688, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.762677"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 0.0064754486083984375, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.769330"}], "B. Grok Integration": [{"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 0.0047321319580078125, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.774432"}], "C. 6-Point Analysis": [{"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "PASS", "response_time": 0.0032978057861328125, "details": {"points_found": 6, "has_6_point_format": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.777946"}], "D. Lee Method": [{"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.0027055740356445312, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.780805"}], "E. Options Trading": [{"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.0032231807708740234, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.784225"}], "F. Market Intelligence": [{"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.9981224536895752, "details": {"has_live_price": true, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.782733"}], "G. Portfolio Risk": [{"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.0052640438079833984, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.788250"}], "H. Proactive Assistant": [{"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.003826618194580078, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.792972"}], "I. Multi-modal Global": [{"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 0.004137754440307617, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:21:36.797346"}], "J. Explainable AI": [{"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.004080772399902344, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:21:36.801643"}], "K. API Endpoints": [{"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.00093841552734375, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:21:36.802786"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/market/quote/AAPL", "status": "PASS", "response_time": 0.0009779930114746094, "details": {"has_price_data": true, "response": {"symbol": "AAPL", "error": "Unable to fetch real-time data", "message": "Market data provider unavailable", "timestamp": "2025-07-24T07:21:36.803509"}}, "error_message": null, "timestamp": "2025-07-24T07:21:36.803913"}], "L. Infrastructure": [{"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "PASS", "response_time": 0.0025789737701416016, "details": {"first_response_time": 0.001409769058227539, "second_response_time": 0.0011692047119140625, "caching_detected": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.806767"}], "M. Security": [{"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.0030443668365478516, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.810273"}]}, "detailed_results": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.0217397212982178, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:21:35.753423"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.002920866012573242, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.756699"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.003603696823120117, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.760413"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.0017499923706054688, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.762677"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 0.0064754486083984375, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.769330"}, {"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 0.0047321319580078125, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.774432"}, {"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "PASS", "response_time": 0.0032978057861328125, "details": {"points_found": 6, "has_6_point_format": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.777946"}, {"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.0027055740356445312, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.780805"}, {"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.0032231807708740234, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:21:35.784225"}, {"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.9981224536895752, "details": {"has_live_price": true, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.782733"}, {"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.0052640438079833984, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.788250"}, {"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.003826618194580078, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.792972"}, {"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 0.004137754440307617, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:21:36.797346"}, {"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.004080772399902344, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:21:36.801643"}, {"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.00093841552734375, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:21:36.802786"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/market/quote/AAPL", "status": "PASS", "response_time": 0.0009779930114746094, "details": {"has_price_data": true, "response": {"symbol": "AAPL", "error": "Unable to fetch real-time data", "message": "Market data provider unavailable", "timestamp": "2025-07-24T07:21:36.803509"}}, "error_message": null, "timestamp": "2025-07-24T07:21:36.803913"}, {"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "PASS", "response_time": 0.0025789737701416016, "details": {"first_response_time": 0.001409769058227539, "second_response_time": 0.0011692047119140625, "caching_detected": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.806767"}, {"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.0030443668365478516, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:21:36.810273"}]}