{"timestamp": "2025-07-24T07:09:39.024191", "summary": {"total_tests": 18, "passed": 12, "failed": 6, "errors": 0, "skipped": 0, "pass_rate": 66.67}, "categories": {"System": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.033345937728882, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:09:35.961541"}], "A. Conversational AI": [{"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.006492137908935547, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:35.968374"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.0031082630157470703, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:35.971633"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.004121541976928711, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:35.975844"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 1.0002880096435547, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.976235"}], "B. Grok Integration": [{"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 0.005087375640869141, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.984934"}], "C. 6-Point Analysis": [{"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "FAIL", "response_time": 0.004081249237060547, "details": {"points_found": 1, "has_6_point_format": false}, "error_message": null, "timestamp": "2025-07-24T07:09:36.989326"}], "D. Lee Method": [{"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.0029478073120117188, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.992491"}], "E. Options Trading": [{"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.003362417221069336, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.996035"}], "F. Market Intelligence": [{"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "FAIL", "response_time": 0.992321252822876, "details": {"has_live_price": false, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:09:37.988587"}], "G. Portfolio Risk": [{"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.0030617713928222656, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:09:37.991921"}], "H. Proactive Assistant": [{"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.009827136993408203, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:09:38.001964"}], "I. Multi-modal Global": [{"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 1.0089271068572998, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:09:39.011108"}], "J. Explainable AI": [{"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.004648685455322266, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:09:39.016651"}], "K. API Endpoints": [{"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010068416595458984, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:09:39.017902"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/quote/AAPL", "status": "FAIL", "response_time": 0.0008966922760009766, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-24T07:09:39.018898"}], "L. Infrastructure": [{"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "FAIL", "response_time": 0.0015625953674316406, "details": {"error1": null, "error2": null}, "error_message": null, "timestamp": "2025-07-24T07:09:39.020640"}], "M. Security": [{"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.003057241439819336, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:09:39.023909"}]}, "detailed_results": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.033345937728882, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:09:35.961541"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.006492137908935547, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:35.968374"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.0031082630157470703, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:35.971633"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.004121541976928711, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:35.975844"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 1.0002880096435547, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.976235"}, {"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 0.005087375640869141, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.984934"}, {"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "FAIL", "response_time": 0.004081249237060547, "details": {"points_found": 1, "has_6_point_format": false}, "error_message": null, "timestamp": "2025-07-24T07:09:36.989326"}, {"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.0029478073120117188, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.992491"}, {"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.003362417221069336, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:09:36.996035"}, {"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "FAIL", "response_time": 0.992321252822876, "details": {"has_live_price": false, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:09:37.988587"}, {"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.0030617713928222656, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:09:37.991921"}, {"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.009827136993408203, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:09:38.001964"}, {"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 1.0089271068572998, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:09:39.011108"}, {"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.004648685455322266, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:09:39.016651"}, {"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010068416595458984, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:09:39.017902"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/quote/AAPL", "status": "FAIL", "response_time": 0.0008966922760009766, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-24T07:09:39.018898"}, {"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "FAIL", "response_time": 0.0015625953674316406, "details": {"error1": null, "error2": null}, "error_message": null, "timestamp": "2025-07-24T07:09:39.020640"}, {"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.003057241439819336, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:09:39.023909"}]}