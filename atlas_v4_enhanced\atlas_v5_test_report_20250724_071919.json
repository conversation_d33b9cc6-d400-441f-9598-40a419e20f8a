{"timestamp": "2025-07-24T07:19:19.944843", "summary": {"total_tests": 18, "passed": 13, "failed": 5, "errors": 0, "skipped": 0, "pass_rate": 72.22}, "categories": {"System": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.0705738067626953, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:19:16.804891"}], "A. Conversational AI": [{"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.02086019515991211, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:16.826829"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.0286865234375, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:16.857024"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.0157012939453125, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:16.873945"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 0.9425861835479736, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.817705"}], "B. Grok Integration": [{"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 0.0846092700958252, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.902617"}], "C. 6-Point Analysis": [{"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "FAIL", "response_time": 0.002936124801635742, "details": {"points_found": 1, "has_6_point_format": false}, "error_message": null, "timestamp": "2025-07-24T07:19:17.905841"}], "D. Lee Method": [{"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.003062009811401367, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.909109"}], "E. Options Trading": [{"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.002948284149169922, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.912272"}], "F. Market Intelligence": [{"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.9125735759735107, "details": {"has_live_price": true, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:19:18.825052"}], "G. Portfolio Risk": [{"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.003137826919555664, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:19:18.828421"}], "H. Proactive Assistant": [{"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.0895376205444336, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:19:18.918951"}], "I. Multi-modal Global": [{"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 0.9198031425476074, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:19:19.839247"}], "J. Explainable AI": [{"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.0977180004119873, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:19:19.937369"}], "K. API Endpoints": [{"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010254383087158203, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:19:19.938614"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/quote/AAPL", "status": "FAIL", "response_time": 0.0009098052978515625, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-24T07:19:19.939618"}], "L. Infrastructure": [{"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "FAIL", "response_time": 0.0015625953674316406, "details": {"error1": null, "error2": null}, "error_message": null, "timestamp": "2025-07-24T07:19:19.941358"}], "M. Security": [{"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.0030584335327148438, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:19:19.944607"}]}, "detailed_results": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.0705738067626953, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:19:16.804891"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.02086019515991211, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:16.826829"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.0286865234375, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:16.857024"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.0157012939453125, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:16.873945"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 0.9425861835479736, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.817705"}, {"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 0.0846092700958252, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.902617"}, {"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "FAIL", "response_time": 0.002936124801635742, "details": {"points_found": 1, "has_6_point_format": false}, "error_message": null, "timestamp": "2025-07-24T07:19:17.905841"}, {"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.003062009811401367, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.909109"}, {"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.002948284149169922, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:19:17.912272"}, {"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.9125735759735107, "details": {"has_live_price": true, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:19:18.825052"}, {"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.003137826919555664, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:19:18.828421"}, {"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.0895376205444336, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:19:18.918951"}, {"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 0.9198031425476074, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:19:19.839247"}, {"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.0977180004119873, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:19:19.937369"}, {"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010254383087158203, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:19:19.938614"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/quote/AAPL", "status": "FAIL", "response_time": 0.0009098052978515625, "details": {"status_code": null}, "error_message": null, "timestamp": "2025-07-24T07:19:19.939618"}, {"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "FAIL", "response_time": 0.0015625953674316406, "details": {"error1": null, "error2": null}, "error_message": null, "timestamp": "2025-07-24T07:19:19.941358"}, {"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.0030584335327148438, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:19:19.944607"}]}