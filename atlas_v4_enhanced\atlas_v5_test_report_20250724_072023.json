{"timestamp": "2025-07-24T07:20:23.630671", "summary": {"total_tests": 18, "passed": 14, "failed": 4, "errors": 0, "skipped": 0, "pass_rate": 77.78}, "categories": {"System": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.030905246734619, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:20:22.570638"}], "A. Conversational AI": [{"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.003405332565307617, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.574385"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.0028526782989501953, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.577355"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.0012135505676269531, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.578667"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 0.004029750823974609, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.582816"}], "B. Grok Integration": [{"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 1.0061829090118408, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.589213"}], "C. 6-Point Analysis": [{"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "FAIL", "response_time": 0.004714488983154297, "details": {"points_found": 0, "has_6_point_format": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.594494"}], "D. Lee Method": [{"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.0037016868591308594, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.598485"}], "E. Options Trading": [{"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.0030994415283203125, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.601891"}], "F. Market Intelligence": [{"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.004590272903442383, "details": {"has_live_price": true, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.607120"}], "G. Portfolio Risk": [{"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.0037505626678466797, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.611089"}], "H. Proactive Assistant": [{"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.0034034252166748047, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.614692"}], "I. Multi-modal Global": [{"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 0.003605365753173828, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.618463"}], "J. Explainable AI": [{"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.004137277603149414, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.623102"}], "K. API Endpoints": [{"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010504722595214844, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:20:23.624410"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/quote/AAPL", "status": "FAIL", "response_time": 0.0009596347808837891, "details": {"has_price_data": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.625464"}], "L. Infrastructure": [{"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "PASS", "response_time": 0.0016663074493408203, "details": {"first_response_time": 0.00084686279296875, "second_response_time": 0.0008194446563720703, "caching_detected": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.627325"}], "M. Security": [{"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.002917051315307617, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.630446"}]}, "detailed_results": [{"category": "System", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 2.030905246734619, "details": {"status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:20:22.570638"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 1", "description": "Hello A.T.L.A.S., how are you today?...", "status": "PASS", "response_time": 0.003405332565307617, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.574385"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 2", "description": "Explain the difference between a market order and ...", "status": "PASS", "response_time": 0.0028526782989501953, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.577355"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 3", "description": "Explain RSI like I'm a complete beginner....", "status": "PASS", "response_time": 0.0012135505676269531, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.578667"}, {"category": "A. Conversational AI", "test_name": "Basic Chat 4", "description": "Now explain Bollinger Bands like I'm an institutio...", "status": "PASS", "response_time": 0.004029750823974609, "details": {"has_response": true}, "error_message": null, "timestamp": "2025-07-24T07:20:22.582816"}, {"category": "B. Grok Integration", "test_name": "Primary Reasoning", "description": "NVDA causal analysis", "status": "PASS", "response_time": 1.0061829090118408, "details": {"has_causal_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.589213"}, {"category": "C. 6-Point Analysis", "test_name": "Full Analysis", "description": "AAPL swing trade analysis", "status": "FAIL", "response_time": 0.004714488983154297, "details": {"points_found": 0, "has_6_point_format": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.594494"}, {"category": "<PERSON><PERSON>", "test_name": "Pattern Detection", "description": "GOOGL Lee Method scan", "status": "PASS", "response_time": 0.0037016868591308594, "details": {"has_lee_method_analysis": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.598485"}, {"category": "E. Options Trading", "test_name": "Black-Scholes Greeks", "description": "MSFT options Greeks", "status": "PASS", "response_time": 0.0030994415283203125, "details": {"has_greeks_calculation": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.601891"}, {"category": "F. Market Intelligence", "test_name": "Live Quotes", "description": "AMZN current price", "status": "PASS", "response_time": 0.004590272903442383, "details": {"has_live_price": true, "response_time_ok": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.607120"}, {"category": "G. <PERSON>", "test_name": "Markowitz Optimization", "description": "Portfolio optimization", "status": "PASS", "response_time": 0.0037505626678466797, "details": {"has_portfolio_optimization": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.611089"}, {"category": "<PERSON>. Proactive Assistant", "test_name": "Morning Briefing", "description": "8 AM briefing", "status": "PASS", "response_time": 0.0034034252166748047, "details": {"has_market_briefing": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.614692"}, {"category": "I. Multi-modal Global", "test_name": "International Markets", "description": "Nikkei 225 analysis", "status": "FAIL", "response_time": 0.003605365753173828, "details": {"has_international_analysis": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.618463"}, {"category": "<PERSON>. Explainable AI", "test_name": "Audit Trail", "description": "TSLA decision audit", "status": "FAIL", "response_time": 0.004137277603149414, "details": {"has_audit_trail": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.623102"}, {"category": "K. API Endpoints", "test_name": "Health Check", "description": "GET /api/v1/health", "status": "PASS", "response_time": 0.0010504722595214844, "details": {"has_status_field": true, "status": "healthy"}, "error_message": null, "timestamp": "2025-07-24T07:20:23.624410"}, {"category": "K. API Endpoints", "test_name": "Quote Endpoint", "description": "GET /api/v1/quote/AAPL", "status": "FAIL", "response_time": 0.0009596347808837891, "details": {"has_price_data": false}, "error_message": null, "timestamp": "2025-07-24T07:20:23.625464"}, {"category": "L. Infrastructure", "test_name": "Caching System", "description": "Duplicate quote requests", "status": "PASS", "response_time": 0.0016663074493408203, "details": {"first_response_time": 0.00084686279296875, "second_response_time": 0.0008194446563720703, "caching_detected": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.627325"}, {"category": "M. Security", "test_name": "SQL Injection Protection", "description": "Malicious input test", "status": "PASS", "response_time": 0.002917051315307617, "details": {"handled_safely": true}, "error_message": null, "timestamp": "2025-07-24T07:20:23.630446"}]}